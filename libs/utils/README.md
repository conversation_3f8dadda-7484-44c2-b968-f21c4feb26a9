# @fincloud/utils

A comprehensive utility library for the FinCloud application providing shared constants, helper functions, and business logic utilities used across the entire project.

## Overview

The `@fincloud/utils` library serves as a centralized collection of utility functions, constants, and helper methods that are commonly used throughout the FinCloud application. It provides functionality for string manipulation, document handling, business case management, folder operations, socket communication, and various application-specific constants.

## Installation

This library is part of the FinCloud monorepo and is automatically available to other libraries and applications within the workspace.

```typescript
import { capitalizeFirstLetter, findFolderById, canPreviewDocument, BUSINESS_CASE_TYPE_LABELS } from '@fincloud/utils';
```

## Categories of Utilities

### 🔤 String Manipulation

#### `capitalizeFirstLetter(string: string): string`

Capitalizes only the first letter of a word while converting the rest to lowercase.

```typescript
import { capitalizeFirstLetter } from '@fincloud/utils';

const result = capitalizeFirstLetter('hELLO wORLD');
// Returns: "Hello world"
```

#### Text Normalization Functions

- **`normalizeBrackets(searchTerm: string): string[]`** - Normalizes different bracket types to round brackets
- **`normalizeQuotes(searchTerm: string): string[]`** - Normalizes various quote styles to standard quotes
- **`normalizeUmlauts(text: string): string[]`** - Converts German umlauts to their ASCII equivalents

```typescript
import { normalizeUmlauts, normalizeQuotes } from '@fincloud/utils';

const umlautResult = normalizeUmlauts('Müller');
// Returns: ['Müller', 'Mueller']

const quoteResult = normalizeQuotes('"Hello"');
// Returns: ['"Hello"']
```

#### `replacePairs(term: string, toBeReplaced: string[], replaceWith: string[]): string`

Replaces pairs of characters in a string with specified replacements.

```typescript
import { replacePairs } from '@fincloud/utils';

const result = replacePairs('Hello {world}', ['{', '}'], ['(', ')']);
// Returns: "Hello (world)"
```

### 📁 Folder and Document Operations

#### `findFolderById(folder: Folder, id: string, path?: string[], breadcrumbs?: FinBreadcrumbItem[])`

Recursively searches through a folder tree structure to find a folder by ID and generates breadcrumbs.

```typescript
import { findFolderById } from '@fincloud/utils';
import { Folder } from '@fincloud/swagger-generator/business-case-manager';

const rootFolder: Folder = {
  id: 'root',
  name: 'Root',
  children: [{ id: 'child1', name: 'Child 1', children: [] }],
};

const result = findFolderById(rootFolder, 'child1');
// Returns: { folder: Folder, breadcrumbs: FinBreadcrumbItem[] }
```

#### Document Utilities

- **`canPreviewDocument(fileExt: string): boolean`** - Checks if a document can be previewed (PDF, TIFF)
- **`getDocumentIconPaths(documentMimeTypes: Record<string, string>): Record<string, string>`** - Maps document MIME types to icon paths

```typescript
import { canPreviewDocument, getDocumentIconPaths } from '@fincloud/utils';

const canPreview = canPreviewDocument('pdf'); // Returns: true
const cannotPreview = canPreviewDocument('docx'); // Returns: false

const iconPaths = getDocumentIconPaths({ doc1: 'application/pdf' });
// Returns: { 'doc1': 'assets/svg/svgDocumentIcon.svg' }
```

### 🏢 Business Logic Constants

#### Business Case Types

```typescript
import { BUSINESS_CASE_TYPE_LABELS, CASE_TYPES } from '@fincloud/utils';

// Localized labels for business case types
console.log(BUSINESS_CASE_TYPE_LABELS[BusinessCaseType.FINANCING_CASE]);
// Returns: "Finanzierung"
```

#### Customer and Participation Labels

```typescript
import { CUSTOMER_TYPE_LABELS, CUSTOMER_SOURCE_TYPE_LABELS, PARTICIPATION_TYPE_LABEL, SNAPSHOT_DEMO_TYPE_LABELS } from '@fincloud/utils';
```

### 📄 Template and Structure Utilities

#### Default Templates

- **`BLANK_TEMPLATE`** - Default template structure for business cases
- **`BLANK_TEMPLATE_CADR`** - Default CADR template structure
- **`INITIAL_FINANCING_STRUCTURE`** - Initial state for financing structures

```typescript
import { BLANK_TEMPLATE, INITIAL_FINANCING_STRUCTURE } from '@fincloud/utils';

// Use as starting point for new business cases
const newBusinessCase = { ...BLANK_TEMPLATE };
const newFinancingStructure = { ...INITIAL_FINANCING_STRUCTURE };
```

### 🔌 Socket Communication

#### Socket Destinations

Constants for WebSocket communication endpoints:

```typescript
import { CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION, PLATFORM_NOTIFICATION_DESTINATION, COMPANY_SOCKET_SUBSCRIPTION_DESTINATION } from '@fincloud/utils';

// Use for socket subscriptions
const chatDestination = CHAT_SOCKET_RECEIVE_MESSAGE_DESTINATION; // 'chat-message'
```

### 📊 File and Media Utilities

#### File Extensions and MIME Types

```typescript
import { FILE_EXTENSIONS_ARRAY, MIME_TYPE_ICON_PATHS } from '@fincloud/utils';

// Array of supported file extensions
const supportedExtensions = FILE_EXTENSIONS_ARRAY;
// ['doc', 'docx', 'pdf', 'txt', 'jpg', 'png', ...]

// Icon paths for different MIME types
const pdfIcon = MIME_TYPE_ICON_PATHS[DocumentMimeType.PDF];
// 'assets/svg/svgDocumentIcon.svg'
```

### ⚙️ Configuration Constants

#### Application Settings

```typescript
import { SCROLL_TO_DELAY, SCROLL_TO_OFFSET, DATA_ROOM_MIN_SEARCH_LENGTH, DEFAULT_USER_NAME } from '@fincloud/utils';

const scrollDelay = SCROLL_TO_DELAY; // 300ms
const minSearchLength = DATA_ROOM_MIN_SEARCH_LENGTH; // Minimum characters for search
```

### 🛠️ Object Utilities

#### Object Helper Class

```typescript
import { ObjectHelper } from '@fincloud/utils';

// Deep clone objects
const cloned = ObjectHelper.cloneDeep(originalObject);

// Check if string is numeric
const isNum = ObjectHelper.isNumeric('123'); // true

// Parse string values to appropriate types
const parsed = ObjectHelper.parseString('true'); // boolean true
const parsedNum = ObjectHelper.parseString('123'); // number 123
```

#### Empty Object Constant

```typescript
import { EMPTY_OBJECT } from '@fincloud/utils';

// Use as default empty object (frozen for immutability)
const defaultState = EMPTY_OBJECT;
```

## Target Audience

This library is designed for use by:

- **Frontend Applications** - Angular components and services
- **State Management** - NgRx effects and reducers
- **Business Logic** - Service layers and data processing
- **UI Components** - Shared component libraries
- **API Integration** - Data transformation and validation

## Dependencies

- **TypeScript** - Full TypeScript support with type definitions
- **Angular i18n** - Uses `$localize` for internationalization
- **Swagger Generated Types** - Integrates with API type definitions
- **Lodash-es** - For deep cloning functionality (via ObjectHelper)

## Development

### Building

```bash
nx build utils
```

### Running Tests

```bash
nx test utils
```

### Linting

```bash
nx lint utils
```

## Best Practices

1. **Import Specific Utilities** - Import only what you need to optimize bundle size
2. **Use TypeScript Types** - All utilities are fully typed for better developer experience
3. **Leverage Constants** - Use provided constants instead of hardcoding values
4. **Follow Naming Conventions** - Constants use UPPER_SNAKE_CASE, functions use camelCase

## Contributing

When adding new utilities:

1. Add the utility function/constant to the appropriate file in `src/lib/`
2. Export it from `src/index.ts`
3. Add comprehensive JSDoc documentation
4. Include unit tests
5. Update this README with usage examples

---

_This library is part of the FinCloud monorepo and follows the established patterns and conventions of the project._
